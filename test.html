<!DOCTYPE html>
<html lang="en">
<head>
 <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
 <!-- <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css"> -->
</head>
<style>
   *{
    margin: 0;
    padding: 0;
    }
</style>

<body>
 <div>
  <div id="content-to-print">
    <div class="print-page">
      <!-- 页眉 -->
      <div class="print-header">页头</div>
      <!-- 内容区域 -->
      <div class="print-content">
        <h1>1</h1>
        <h1>2</h1>
        <h1>3</h1>
        <h1>4</h1>
        <h1>5</h1>
        <h1>6</h1>
        <h1>7</h1>
        <h1>8</h1>
        <h1>9</h1>
        <h1>10</h1>
        <h1>11</h1>
        <h1>12</h1>
        <h1>13</h1>
        <h1>14</h1>
        <h1>15</h1>
        <h1>16</h1>
        <h1>17</h1>
        <h1>18</h1>
        <h1>19</h1>
        <h1>20</h1>
        <h1>21</h1>
        <h1>22</h1>
        <h1>23</h1>
        <h1>24</h1>
        <h1>25</h1>
        <h1>26</h1>
        <h1>27</h1>
        <h1>28</h1>
        <h1>29</h1>
        <h1>30</h1>
        <h1>31</h1>
        <h1>32</h1>
        <h1>33</h1>
        <h1>34</h1>
        <h1>35</h1>
        <h1>36</h1>
        <h1>37</h1>
        <h1>38</h1>
        <h1>39</h1>
        <h1>40</h1>
      </div>
      <!-- 页脚 -->
      <div class="print-footer">页脚</div>
    </div>
  </div>
 </div>

 <button onclick="printDocument()" class="btn">打印</button>

 <script>

  function printDocument() {
    const dom = document.getElementById('content-to-print').querySelector('.print-content');
    console.log(dom);
  
   // 设置打印日期
  //  document.getElementById('date').innerText = new Date().toLocaleDateString();

   // 调用 printjs
   printJS({
    printable: 'content-to-print',
    type: 'html',
    targetStyles: ['*'],
    style: `
    @media print {
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      @page {
        size: A4 portrait;
        margin: 20px;
      }

      html, body {
        height: 100%;
        font-family: Arial, sans-serif;
      }

      /* 使用 Flexbox 布局确保页脚在底部 */
      .print-page {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .print-header {
        flex: 0 0 auto; /* 不伸缩，固定高度 */
        height: 60px;
        background: white;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 2px solid #000;
        font-weight: bold;
        font-size: 16px;
      }

      .print-content {
        flex: 1 1 auto; /* 自动伸缩，占满剩余空间 */
        padding: 20px;
        overflow: hidden;
      }

      .print-content h1 {
        margin: 10px 0;
        page-break-inside: avoid;
        font-size: 18px;
      }

      .print-footer {
        flex: 0 0 auto; /* 不伸缩，固定高度 */
        height: 60px;
        background: white;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 2px solid #000;
        font-weight: bold;
        font-size: 16px;
        margin-top: auto; /* 关键：推到底部 */
      }

      /* 确保在分页时页眉页脚正确显示 */
      .print-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: white;
        border-bottom: 2px solid #000;
        padding: 15px 20px;
        text-align: center;
        /* 12行页眉，每行18px高度 + padding */
        height: 248px;
      }

      .print-header div {
        margin: 1px 0;
        font-weight: bold;
        font-size: 14px;
        line-height: 18px;
      }

      .print-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: white;
        border-top: 2px solid #000;
        padding: 15px 20px;
        text-align: center;
        /* 6行页脚，每行18px高度 + padding */
        height: 140px;
      }

      .print-footer div {
        margin: 1px 0;
        font-weight: bold;
        font-size: 14px;
        line-height: 18px;
      }

      .print-content {
        margin-top: 268px;  /* 页眉高度248px + 20px安全边距 */
        margin-bottom: 160px; /* 页脚高度140px + 20px安全边距 */
        min-height: calc(100vh - 428px);
        padding: 0 20px;
      }

      .print-content h1 {
        margin: 8px 0;
        font-size: 16px;
        line-height: 1.4;
        page-break-inside: avoid;
      }
    }
    `
   });
  }
 </script>
</body>
</html>