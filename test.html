<!DOCTYPE html>
<html lang="en">
<head>
 <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
 <!-- <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css"> -->
</head>
<style>
   *{
    margin: 0;
    padding: 0;
    }
</style>

<body>
 <div>
  <div id="content-to-print">
    <!-- 页眉 -->
    <div class="print-header">页头</div>

    <!-- 内容区域 -->
    <div class="print-content">
      <!-- 长表格会自动分页 -->
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1><h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1><h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
      <h1>1</h1>
    </div>

    <!-- 页脚 -->
    <div class="print-footer">页脚</div>
  </div>
 </div>

 <button onclick="printDocument()" class="btn">打印</button>

 <script>

  function printDocument() {
    const dom = document.getElementById('content-to-print').querySelector('.print-content');
    console.log(dom);
  
   // 设置打印日期
  //  document.getElementById('date').innerText = new Date().toLocaleDateString();

   // 调用 printjs
   printJS({
    printable: 'content-to-print',
    type: 'html',
    targetStyles: ['*'],
    style: `
    @media print {
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      @page {
        size: A4 portrait;
        margin: 80px 20px 80px 20px; /* 上下留出页眉页脚空间 */
      }

      body {
        font-family: Arial, sans-serif;
      }

      .print-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 60px;
        background: white;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #000;
        font-weight: bold;
        font-size: 16px;
      }

      .print-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 60px;
        background: white;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 1px solid #000;
        font-weight: bold;
        font-size: 16px;
      }

      .print-content {
        width: 100%;
        padding-top: 80px;  /* 为页眉留出空间 */
        padding-bottom: 80px; /* 为页脚留出空间 */
        min-height: calc(100vh - 160px);
      }

      .print-content h1 {
        margin: 10px 0;
        page-break-inside: avoid;
      }

      /* 确保页面分页时页眉页脚正确显示 */
      .print-content::before {
        content: "";
        display: block;
        height: 0;
        page-break-before: always;
      }

      .print-content::before:first-child {
        page-break-before: avoid;
      }
    }
    `
   });
  }
 </script>
</body>
</html>