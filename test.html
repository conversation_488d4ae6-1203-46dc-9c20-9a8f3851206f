<!DOCTYPE html>
<html lang="en">
<head>
 <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
 <!-- <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css"> -->
</head>
<style>
   *{
    margin: 0;
    padding: 0;
    }
</style>

<body>
 <div>
  <div id="content-to-print">
    <div class="print-page">
      <!-- 页眉 -->
      <div class="print-header">页头</div>

      <!-- 内容区域 -->
      <div class="print-content">
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1><h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1><h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
        <h1>1</h1>
      </div>

      <!-- 页脚 -->
      <div class="print-footer">页脚</div>
    </div>
  </div>
 </div>

 <button onclick="printDocument()" class="btn">打印</button>

 <script>

  function printDocument() {
    const dom = document.getElementById('content-to-print').querySelector('.print-content');
    console.log(dom);
  
   // 设置打印日期
  //  document.getElementById('date').innerText = new Date().toLocaleDateString();

   // 调用 printjs
   printJS({
    printable: 'content-to-print',
    type: 'html',
    targetStyles: ['*'],
    style: `
    @media print {
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      @page {
        size: A4 portrait;
        margin: 20px;
      }

      html, body {
        height: 100%;
        font-family: Arial, sans-serif;
      }

      /* 使用 Flexbox 布局确保页脚在底部 */
      .print-page {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .print-header {
        flex: 0 0 auto; /* 不伸缩，固定高度 */
        height: 60px;
        background: white;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 2px solid #000;
        font-weight: bold;
        font-size: 16px;
      }

      .print-content {
        flex: 1 1 auto; /* 自动伸缩，占满剩余空间 */
        padding: 20px;
        overflow: hidden;
      }

      .print-content h1 {
        margin: 10px 0;
        page-break-inside: avoid;
        font-size: 18px;
      }

      .print-footer {
        flex: 0 0 auto; /* 不伸缩，固定高度 */
        height: 60px;
        background: white;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 2px solid #000;
        font-weight: bold;
        font-size: 16px;
        margin-top: auto; /* 关键：推到底部 */
      }

      /* 确保在分页时页眉页脚正确显示 */
      .print-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
      }

      .print-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
      }

      .print-content {
        margin-top: 80px;  /* 为页眉留出空间 */
        margin-bottom: 80px; /* 为页脚留出空间 */
        min-height: calc(100vh - 160px);
      }
    }
    `
   });
  }
 </script>
</body>
</html>