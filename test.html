<!DOCTYPE html>
<html lang="en">
<head>
 <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
 <!-- <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css"> -->
</head>
<style>
   *{
    margin: 0;
    padding: 0;
    }
</style>

<body>
 <div>
  <div id="content-to-print">
    <table>
      <thead>
        <th>
          <div class="print-header">页头</div>
        </th>
      </thead>
      <tbody>
        <tr>
          <td>
            <!-- 内容区域 -->
            <div class="print-content">
              <!-- 长表格会自动分页 -->
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1><h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1><h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
              <h1>1</h1>
            </div>
          </td>
        </tr>
      </tbody>
      <tfoot>
        <th>
          <div class="print-footer">
            页脚
          </div>
        </th>
      </tfoot>
    </table>
  </div>
 </div>

 <button onclick="printDocument()" class="btn">打印</button>

 <script>

  function printDocument() {
    const dom = document.getElementById('content-to-print').querySelector('.print-content');
    console.log(dom);
  
   // 设置打印日期
  //  document.getElementById('date').innerText = new Date().toLocaleDateString();

   // 调用 printjs
   printJS({
    printable: 'content-to-print',
    type: 'html',
    targetStyles: ['*'],
    style: `  @media print {
    *{
    margin: 0;
    padding: 0;
    }
    table{
     width: 100%;
     height: 2040px;
    }
   @page {
    size: A4 portrait;
    margin-top: 0;
    margin-bottom: 0;
   

   }
   .print-header{
    width: 100%;
     height: 100%
     
   }
   .print-content{
    border: 1px solid red;
    height: 100%
     page-break-after: always;
   }
   
   .print-footer{
     width: 100%;
     height: 100%;
     border: 1px solid;

    
   }
  }
`
   });
  }
 </script>
</body>
</html>