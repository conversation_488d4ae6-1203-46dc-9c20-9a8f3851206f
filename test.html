<!DOCTYPE html>
<html lang="en">
<head>
 <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
 <!-- <link rel="stylesheet" href="https://printjs-4de6.kxcdn.com/print.min.css"> -->
</head>
<style>
   *{
    margin: 0;
    padding: 0;
    }
</style>

<body>
 <div>
  <div id="content-to-print">
    <table class="print-table">
      <thead>
        <tr>
          <th class="print-header">页头</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td class="print-content">
            <!-- 内容区域 -->
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1><h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1><h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
            <h1>1</h1>
          </td>
        </tr>
      </tbody>
      <tfoot>
        <tr>
          <th class="print-footer">页脚</th>
        </tr>
      </tfoot>
    </table>
  </div>
 </div>

 <button onclick="printDocument()" class="btn">打印</button>

 <script>

  function printDocument() {
    const dom = document.getElementById('content-to-print').querySelector('.print-content');
    console.log(dom);
  
   // 设置打印日期
  //  document.getElementById('date').innerText = new Date().toLocaleDateString();

   // 调用 printjs
   printJS({
    printable: 'content-to-print',
    type: 'html',
    targetStyles: ['*'],
    style: `
    @media print {
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      @page {
        size: A4 portrait;
        margin: 20px;
      }

      body {
        font-family: Arial, sans-serif;
      }

      /* 表格布局 */
      .print-table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed;
        height: 100vh; /* 占满整个视口高度 */
      }

      /* 页眉样式 - 关键：使用 thead 的打印重复特性 */
      .print-table thead {
        display: table-header-group; /* 确保在每页顶部重复 */
      }

      .print-header {
        height: 60px;
        background: white;
        text-align: center;
        vertical-align: middle;
        border-bottom: 2px solid #000;
        font-weight: bold;
        font-size: 16px;
        padding: 10px;
      }

      /* 页脚样式 - 关键：使用 tfoot 的打印重复特性 */
      .print-table tfoot {
        display: table-footer-group; /* 确保在每页底部重复 */
      }

      .print-footer {
        height: 60px;
        background: white;
        text-align: center;
        vertical-align: middle;
        border-top: 2px solid #000;
        font-weight: bold;
        font-size: 16px;
        padding: 10px;
      }

      /* 内容区域 */
      .print-content {
        vertical-align: top;
        padding: 20px;
        height: auto;
      }

      .print-content h1 {
        margin: 10px 0;
        page-break-inside: avoid;
        font-size: 18px;
      }

      /* 确保表格在分页时正确处理 */
      .print-table tbody {
        display: table-row-group;
      }

      /* 防止表格行在页面边界处断开 */
      .print-table tr {
        page-break-inside: avoid;
      }
    }
    `
   });
  }
 </script>
</body>
</html>